import React, { useEffect, useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Github,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Calendar,
  ArrowRight,
  Download,
  Briefcase,
  GitBranch,
  Sun,
  Moon,
  Search,
  Filter,
  Users,
  Building,
  Handshake,
  Eye,
  Code,
  Globe,
  Award,
  Target,
  Heart,
  Zap
} from "lucide-react";
import { But<PERSON> } from "./components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "./components/ui/card";
import { Badge } from "./components/ui/badge";
import { Input } from "./components/ui/input";

// ----------------------------
// Static Data (Portfolio)
// ----------------------------
const profile = {
  name: "<PERSON><PERSON><PERSON>",
  title: "Full-Stack Developer | AI & CV Enthusiast",
  summary:
    "Secure software enthusiast with experience in backend, AI/ML, and real-world applications. Skilled in building scalable systems and solving problems through innovation.",
  email: "<EMAIL>",
  phone: "+91 6281399603",
  location: "Karimnagar, Telangana, India (505001)",
  links: {
    github: "https://github.com/pavankontham",
    linkedin: "https://linkedin.com/in/pavan-kontham-911456276",
  },
  // Role-specific content
  roleContent: {
    employer: {
      bio: "Experienced full-stack developer with a proven track record of delivering scalable applications and AI-driven solutions. Strong background in modern web technologies, machine learning, and system architecture. Ready to contribute to innovative teams and drive technical excellence.",
      cta: "View My Resume",
      ctaSecondary: "Schedule Interview"
    },
    client: {
      bio: "Professional software consultant specializing in custom web applications, AI integration, and digital transformation. I help businesses leverage cutting-edge technology to solve complex problems and achieve their goals.",
      cta: "Start Your Project",
      ctaSecondary: "Get Quote"
    },
    collaborator: {
      bio: "Passionate developer and innovator looking to collaborate on exciting projects. Experienced in full-stack development, AI/ML, and emerging technologies. Let's build something amazing together!",
      cta: "Let's Collaborate",
      ctaSecondary: "View GitHub"
    },
    general: {
      bio: "Welcome to my portfolio! I'm a full-stack developer passionate about creating innovative solutions that make a difference. Explore my projects and feel free to reach out.",
      cta: "Explore Projects",
      ctaSecondary: "Get in Touch"
    }
  },
  projects: [
    // Top Highlights
    {
      name: "Voice-Based Finance Assistant (Morning Market Brief)",
      year: 2025,
      category: "AI/ML",
      type: "featured",
      stack: ["Streamlit", "FastAPI", "LangChain", "CrewAI", "FAISS", "Whisper"],
      highlights: [
        "Built a multi-agent assistant to fetch, analyze, and summarize financial news from multiple sources in real-time using RAG, STT, and TTS workflows. Delivered personalized spoken market briefs through dynamic user query handling."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["AI", "Finance", "Voice", "Real-time", "Multi-agent"]
    },
    {
      name: "Emotion-Aware Music Therapy System (EmotiTune)",
      year: 2025,
      category: "AI/ML",
      type: "featured",
      stack: ["Python", "OpenCV", "Wav2Vec", "React", "Firebase", "LLMs", "MuseNet", "Riffusion"],
      highlights: [
        "Developed a multimodal platform detecting emotions from facial, audio, and text inputs, generating and recommending music aligned to user moods. Designed for therapeutic and personalized music interventions."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["AI", "Healthcare", "Music", "Computer Vision", "Emotion Detection"]
    },
    {
      name: "AI Learning Platform",
      year: 2025,
      category: "Full-Stack",
      type: "featured",
      stack: ["Java", "Spring Boot", "React", "JWT", "Gemini API"],
      highlights: [
        "Built a full-stack AI-driven learning system generating custom study material, quizzes, and assignments. Integrated APIs for YouTube, Codeforces, and Gemini to deliver adaptive workflows and a responsive frontend."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Education", "AI", "Full-Stack", "API Integration"]
    },
    {
      name: "Rubik’s Cube Solver Web Application",
      year: 2025,
      stack: ["Three.js", "JavaScript", "Python Flask"],
      highlights: [
        "Created a solver supporting 2x2, 3x3, and 4x4 Rubik’s cubes with 2D and 3D visualizations, scramble generation, and auto/manual solving using Kociemba’s algorithm."
      ]
    },
    {
      name: "MediQuick AI+ (Healthcare AI Project)",
      year: 2025,
      stack: ["Deep Learning", "OCR", "NLP", "Knowledge Graphs"],
      highlights: [
        "Implemented handwritten prescription recognition, drug-drug interaction prediction using knowledge graphs, and multimodal recognition pipelines to improve efficiency in healthcare services."
      ]
    },
    {
      name: "WattWise (GreenCode Challenge – 2nd Prize)",
      year: 2025,
      stack: ["React Native", "Firebase", "Node.js", "Chart.js", "TailwindCSS"],
      highlights: [
        "Developed a cross-platform app for monitoring real-time electricity usage. Delivered personalized dashboards, gamified savings, and interactive charts to promote sustainable energy habits."
      ]
    },

    // Additional Projects
    {
      name: "Anime Cannon – Anime Streaming Website",
      year: 2023,
      stack: ["HTML", "CSS", "JavaScript"],
      highlights: ["Built an anime streaming platform with genre-based filtering and search functionality for enhanced user experience."]
    },
    {
      name: "Game Arena – 6-in-1 Java Games",
      year: 2023,
      stack: ["Java"],
      highlights: ["Created a desktop gaming app featuring 6 classic games including Snake, Flappy Bird, and Tic-Tac-Toe."]
    },
    {
      name: "Voice-to-Text Conversion",
      year: 2023,
      stack: ["Python"],
      highlights: ["Implemented a Python-based project converting voice input into text using speech recognition."]
    },
    {
      name: "Text-to-Audio & Audio-to-Text",
      year: 2023,
      stack: ["Python"],
      highlights: ["Developed bi-directional conversion between audio and text to enable voice-driven applications."]
    },
    {
      name: "Hand Gesture Recognition Model",
      year: 2023,
      stack: ["Python", "CNN"],
      highlights: ["Designed a CNN model for accurate classification of hand gestures, forming the base for gesture-based control systems."]
    },
    {
      name: "Mouse Control via Hand Gestures",
      year: 2023,
      stack: ["Python", "OpenCV"],
      highlights: ["Implemented gesture-controlled mouse navigation using OpenCV for real-time tracking."]
    },
    {
      name: "Image Captioning using BLIP Model",
      year: 2024,
      stack: ["Python", "Deep Learning"],
      highlights: ["Created an image captioning system with BLIP fine-tuned model, generating captions for uploaded images and live camera feeds."]
    },
    {
      name: "Virtual Health Assistant Bot",
      year: 2023,
      stack: ["Python", "Gemini GPT"],
      highlights: ["Built a healthcare chatbot capable of handling patient queries with AI-powered responses."]
    },
    {
      name: "Smart Attendance System",
      year: 2024,
      stack: ["React", "Supabase", "Python", "OpenCV", "MediaPipe"],
      highlights: ["Developed real-time attendance tracking with liveness detection to prevent spoofing and automated logging into databases."]
    },
    {
      name: "Cybersecurity Awareness Campaign",
      year: 2023,
      stack: ["Social Project"],
      highlights: ["Organized and led an outreach campaign teaching cybersecurity practices to school children."]
    }
  ],
  lastUpdated: "Aug 29, 2025"
};

// ---------------
// UI Utilities
// ---------------
const Section = ({ id, title, icon: Icon, children }) => (
  <section id={id} className="py-14 scroll-mt-24">
    <div className="max-w-6xl mx-auto px-4">
      <div className="flex items-center gap-3 mb-6">
        {Icon && <Icon className="w-6 h-6" />}
        <h2 className="text-2xl font-semibold">{title}</h2>
      </div>
      {children}
    </div>
  </section>
);

const Pill = ({ children }) => (
  <Badge className="rounded-2xl px-3 py-1 text-xs">{children}</Badge>
);

function useTheme() {
  const [theme, setTheme] = useState(() =>
    typeof window !== "undefined" && window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light"
  );
  useEffect(() => {
    document.documentElement.classList.toggle("dark", theme === "dark");
  }, [theme]);
  return { theme, setTheme };
}

// ---------------
// Main Component
// ---------------
export default function Portfolio() {
  const { theme, setTheme } = useTheme();
  const [query, setQuery] = useState("");
  const [filter, setFilter] = useState("All");

  const filteredProjects = useMemo(() => {
    const q = query.toLowerCase();
    return profile.projects.filter(
      (p) =>
        (filter === "All" || p.stack.includes(filter)) &&
        (p.name.toLowerCase().includes(q) ||
          p.stack.join(" ").toLowerCase().includes(q) ||
          p.highlights.join(" ").toLowerCase().includes(q))
    );
  }, [query, filter]);

  const techTags = ["All", ...new Set(profile.projects.flatMap((p) => p.stack))];

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Top Nav */}
      <div className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b">
        <div className="max-w-6xl mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center gap-2 font-semibold">
            <GitBranch className="w-5 h-5" /> Pavan.dev
          </div>
          <nav className="hidden md:flex items-center gap-4">
            <a href="#projects" className="text-sm hover:underline">Projects</a>
            <a href="#contact" className="text-sm hover:underline">Contact</a>
          </nav>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" onClick={() => setTheme(theme === "dark" ? "light" : "dark")}>
              {theme === "dark" ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </Button>
            <a href={profile.links.github} target="_blank" rel="noreferrer"><Button variant="ghost" size="icon"><Github className="w-5 h-5" /></Button></a>
            <a href={profile.links.linkedin} target="_blank" rel="noreferrer"><Button variant="ghost" size="icon"><Linkedin className="w-5 h-5" /></Button></a>
          </div>
        </div>
      </div>

      {/* Projects */}
      <Section id="projects" title="Projects" icon={Briefcase}>
        <div className="flex flex-col md:flex-row md:items-center gap-3 mb-4">
          <div className="flex items-center gap-2 flex-1">
            <Search className="w-4 h-4" />
            <Input placeholder="Search by name, stack, or keywords…" value={query} onChange={(e) => setQuery(e.target.value)} />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <select value={filter} onChange={(e) => setFilter(e.target.value)} className="border rounded-xl px-3 py-1">
              {techTags.map((t) => <option key={t} value={t}>{t}</option>)}
            </select>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((p, idx) => (
            <motion.div key={idx} initial={{ opacity: 0, y: 10 }} whileInView={{ opacity: 1, y: 0 }} viewport={{ once: true }} transition={{ duration: 0.35 }}>
              <Card className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between gap-2">
                    <span>{p.name}</span>
                    <Badge variant="outline">{p.year}</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  <div className="flex flex-wrap gap-2">{p.stack.map((s) => (<Pill key={s}>{s}</Pill>))}</div>
                  <ul className="list-disc pl-5 text-sm text-muted-foreground">
                    {p.highlights.map((h, i) => (<li key={i}>{h}</li>))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </Section>

      {/* Contact */}
      <Section id="contact" title="Contact" icon={Mail}>
        <Card>
          <CardContent className="space-y-3 text-sm">
            <div className="flex items-center gap-2"><Mail className="w-4 h-4" /> {profile.email}</div>
            <div className="flex items-center gap-2"><Phone className="w-4 h-4" /> {profile.phone}</div>
            <div className="flex items-center gap-2"><MapPin className="w-4 h-4" /> {profile.location}</div>
          </CardContent>
        </Card>
      </Section>

      {/* Footer */}
      <footer className="border-t mt-10">
        <div className="max-w-6xl mx-auto px-4 py-8 text-sm flex flex-col md:flex-row items-center justify-between gap-3">
          <div>© {new Date().getFullYear()} Kontham Pavan. All rights reserved.</div>
          <div className="flex items-center gap-2">
            <a className="hover:underline" href="#projects">Projects</a>
            <a className="hover:underline" href="#contact">Contact</a>
          </div>
        </div>
      </footer>

      <style>{`
        :root {
          color-scheme: light dark;
        }
        html {
          scroll-behavior: smooth;
        }
      `}</style>
    </div>
  );
}

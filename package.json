{"name": "professional-portfolio", "version": "1.0.0", "description": "Professional portfolio website with dynamic content loading", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-next": "^14.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.2.0"}, "keywords": ["portfolio", "react", "nextjs", "professional", "dynamic-content"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}
import React, { useEffect, useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Github,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Calendar,
  ArrowRight,
  Download,
  Briefcase,
  GitBranch,
  Sun,
  Moon,
  Search,
  Filter,
  Users,
  Building,
  Handshake,
  Eye,
  Code,
  Globe,
  Award,
  Target,
  Heart,
  Zap
} from "lucide-react";
import { But<PERSON> } from "./components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "./components/ui/card";
import { Badge } from "./components/ui/badge";
import { Input } from "./components/ui/input";

// ----------------------------
// Enhanced Profile Data
// ----------------------------
const profile = {
  name: "<PERSON><PERSON>ham Pavan",
  title: "Full-Stack Developer | AI & CV Enthusiast",
  summary: "Secure software enthusiast with experience in backend, AI/ML, and real-world applications. Skilled in building scalable systems and solving problems through innovation.",
  email: "<EMAIL>",
  phone: "+91 6281399603",
  location: "Karimnagar, Telangana, India (505001)",
  links: {
    github: "https://github.com/pavankontham",
    linkedin: "https://linkedin.com/in/pavan-kontham-911456276",
  },

  // Role-specific content
  roleContent: {
    employer: {
      bio: "Experienced full-stack developer with a proven track record of delivering scalable applications and AI-driven solutions. Strong background in modern web technologies, machine learning, and system architecture. Ready to contribute to innovative teams and drive technical excellence.",
      cta: "View My Resume",
      ctaSecondary: "Schedule Interview"
    },
    client: {
      bio: "Professional software consultant specializing in custom web applications, AI integration, and digital transformation. I help businesses leverage cutting-edge technology to solve complex problems and achieve their goals.",
      cta: "Start Your Project",
      ctaSecondary: "Get Quote"
    },
    collaborator: {
      bio: "Passionate developer and innovator looking to collaborate on exciting projects. Experienced in full-stack development, AI/ML, and emerging technologies. Let's build something amazing together!",
      cta: "Let's Collaborate",
      ctaSecondary: "View GitHub"
    },
    general: {
      bio: "Welcome to my portfolio! I'm a full-stack developer passionate about creating innovative solutions that make a difference. Explore my projects and feel free to reach out.",
      cta: "Explore Projects",
      ctaSecondary: "Get in Touch"
    }
  },

  projects: [
    {
      name: "Voice-Based Finance Assistant (Morning Market Brief)",
      year: 2025,
      category: "AI/ML",
      type: "featured",
      stack: ["Streamlit", "FastAPI", "LangChain", "CrewAI", "FAISS", "Whisper"],
      highlights: [
        "Built a multi-agent assistant to fetch, analyze, and summarize financial news from multiple sources in real-time using RAG, STT, and TTS workflows. Delivered personalized spoken market briefs through dynamic user query handling."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["AI", "Finance", "Voice", "Real-time", "Multi-agent"],
      relevantFor: ["employer", "client", "collaborator"]
    },
    {
      name: "Emotion-Aware Music Therapy System (EmotiTune)",
      year: 2025,
      category: "AI/ML",
      type: "featured",
      stack: ["Python", "OpenCV", "Wav2Vec", "React", "Firebase", "LLMs", "MuseNet", "Riffusion"],
      highlights: [
        "Developed a multimodal platform detecting emotions from facial, audio, and text inputs, generating and recommending music aligned to user moods. Designed for therapeutic and personalized music interventions."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["AI", "Healthcare", "Music", "Computer Vision", "Emotion Detection"],
      relevantFor: ["employer", "client", "collaborator"]
    },
    {
      name: "AI Learning Platform",
      year: 2025,
      category: "Full-Stack",
      type: "featured",
      stack: ["Java", "Spring Boot", "React", "JWT", "Gemini API"],
      highlights: [
        "Built a full-stack AI-driven learning system generating custom study material, quizzes, and assignments. Integrated APIs for YouTube, Codeforces, and Gemini to deliver adaptive workflows and a responsive frontend."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Education", "AI", "Full-Stack", "API Integration"],
      relevantFor: ["employer", "client"]
    },
    {
      name: "Rubik's Cube Solver Web Application",
      year: 2025,
      category: "Web Development",
      type: "featured",
      stack: ["Three.js", "JavaScript", "Python Flask"],
      highlights: [
        "Created a solver supporting 2x2, 3x3, and 4x4 Rubik's cubes with 2D and 3D visualizations, scramble generation, and auto/manual solving using Kociemba's algorithm."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["3D Graphics", "Algorithm", "Interactive", "Puzzle"],
      relevantFor: ["employer", "collaborator"]
    },
    {
      name: "MediQuick AI+ (Healthcare AI Project)",
      year: 2025,
      category: "AI/ML",
      type: "featured",
      stack: ["Deep Learning", "OCR", "NLP", "Knowledge Graphs"],
      highlights: [
        "Implemented handwritten prescription recognition, drug-drug interaction prediction using knowledge graphs, and multimodal recognition pipelines to improve efficiency in healthcare services."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Healthcare", "OCR", "Knowledge Graphs", "Medical AI"],
      relevantFor: ["employer", "client"]
    },
    {
      name: "WattWise (GreenCode Challenge – 2nd Prize)",
      year: 2025,
      category: "Mobile Development",
      type: "award",
      stack: ["React Native", "Firebase", "Node.js", "Chart.js", "TailwindCSS"],
      highlights: [
        "Developed a cross-platform app for monitoring real-time electricity usage. Delivered personalized dashboards, gamified savings, and interactive charts to promote sustainable energy habits."
      ],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Sustainability", "Mobile", "Real-time", "Gamification", "Award Winner"],
      relevantFor: ["employer", "client", "collaborator"]
    },
    {
      name: "Smart Attendance System",
      year: 2024,
      category: "Computer Vision",
      type: "standard",
      stack: ["React", "Supabase", "Python", "OpenCV", "MediaPipe"],
      highlights: ["Developed real-time attendance tracking with liveness detection to prevent spoofing and automated logging into databases."],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Computer Vision", "Real-time", "Security", "Database"],
      relevantFor: ["employer", "client"]
    },
    {
      name: "Image Captioning using BLIP Model",
      year: 2024,
      category: "AI/ML",
      type: "standard",
      stack: ["Python", "Deep Learning"],
      highlights: ["Created an image captioning system with BLIP fine-tuned model, generating captions for uploaded images and live camera feeds."],
      demoUrl: "#",
      sourceUrl: "https://github.com/pavankontham",
      image: "/api/placeholder/400/250",
      tags: ["Computer Vision", "Deep Learning", "Image Processing"],
      relevantFor: ["employer", "collaborator"]
    }
  ],
  lastUpdated: "Aug 29, 2025"
};

// User intent options
const userIntents = [
  {
    id: "employer",
    title: "Potential Employer",
    description: "Looking to hire a talented developer",
    icon: Building,
    color: "bg-blue-500"
  },
  {
    id: "client",
    title: "Client Seeking Services",
    description: "Need custom software solutions",
    icon: Briefcase,
    color: "bg-green-500"
  },
  {
    id: "collaborator",
    title: "Collaborator/Partner",
    description: "Want to work together on projects",
    icon: Handshake,
    color: "bg-purple-500"
  },
  {
    id: "general",
    title: "General Visitor",
    description: "Just exploring and learning more",
    icon: Eye,
    color: "bg-orange-500"
  }
];

// ---------------
// UI Utilities
// ---------------
const Section = ({ id, title, icon: Icon, children, className = "" }) => (
  <section id={id} className={`py-14 scroll-mt-24 ${className}`}>
    <div className="max-w-6xl mx-auto px-4">
      {title && (
        <div className="flex items-center gap-3 mb-8">
          {Icon && <Icon className="w-6 h-6" />}
          <h2 className="text-3xl font-bold">{title}</h2>
        </div>
      )}
      {children}
    </div>
  </section>
);

const Pill = ({ children, variant = "default" }) => (
  <Badge variant={variant} className="rounded-full px-3 py-1 text-xs font-medium">
    {children}
  </Badge>
);

function useTheme() {
  const [theme, setTheme] = useState(() =>
    typeof window !== "undefined" && window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light"
  );
  useEffect(() => {
    document.documentElement.classList.toggle("dark", theme === "dark");
  }, [theme]);
  return { theme, setTheme };
}

// ---------------
// User Intent Selection Component
// ---------------
const UserIntentSelector = ({ onSelectIntent }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-8"
    >
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold text-foreground">
          Who are you looking to connect with?
        </h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Help me tailor the experience to show you the most relevant information
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto">
        {userIntents.map((intent) => {
          const IconComponent = intent.icon;
          return (
            <motion.div
              key={intent.id}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/50"
                onClick={() => onSelectIntent(intent.id)}
              >
                <CardContent className="p-6 text-center space-y-4">
                  <div className={`w-16 h-16 ${intent.color} rounded-full flex items-center justify-center mx-auto`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{intent.title}</h3>
                    <p className="text-sm text-muted-foreground">{intent.description}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 mx-auto text-muted-foreground" />
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
};

// ---------------
// Hero Section Component
// ---------------
const HeroSection = ({ selectedIntent, onReset }) => {
  const content = profile.roleContent[selectedIntent] || profile.roleContent.general;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-8"
    >
      <div className="space-y-4">
        <motion.h1
          className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {profile.name}
        </motion.h1>
        <motion.p
          className="text-xl text-muted-foreground"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {profile.title}
        </motion.p>
        <motion.p
          className="text-lg max-w-3xl mx-auto leading-relaxed"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {content.bio}
        </motion.p>
      </div>

      <motion.div
        className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Button size="lg" className="px-8">
          {content.cta} <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
        <Button variant="outline" size="lg" className="px-8">
          {content.ctaSecondary}
        </Button>
        <Button variant="ghost" onClick={onReset} className="text-sm">
          Change Selection
        </Button>
      </motion.div>
    </motion.div>
  );
};


// ---------------
// Project Card Component
// ---------------
const ProjectCard = ({ project, selectedIntent }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
      whileHover={{ y: -5 }}
      className="h-full"
    >
      <Card className="h-full overflow-hidden group hover:shadow-xl transition-all duration-300">
        <div className="relative overflow-hidden">
          <img
            src={project.image}
            alt={project.name}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-4 right-4 flex gap-2">
            {project.type === "featured" && (
              <Badge className="bg-yellow-500 text-yellow-50">
                <Award className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            {project.type === "award" && (
              <Badge className="bg-purple-500 text-purple-50">
                <Target className="w-3 h-3 mr-1" />
                Award Winner
              </Badge>
            )}
          </div>
        </div>

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between gap-2">
            <CardTitle className="text-lg leading-tight">{project.name}</CardTitle>
            <Badge variant="outline" className="shrink-0">{project.year}</Badge>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Badge variant="secondary" className="text-xs">{project.category}</Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-1.5">
            {project.stack.slice(0, 4).map((tech) => (
              <Pill key={tech} variant="outline">{tech}</Pill>
            ))}
            {project.stack.length > 4 && (
              <Pill variant="outline">+{project.stack.length - 4} more</Pill>
            )}
          </div>

          <div className="text-sm text-muted-foreground line-clamp-3">
            {project.highlights[0]}
          </div>

          <div className="flex flex-wrap gap-1">
            {project.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs px-2 py-0.5">
                {tag}
              </Badge>
            ))}
          </div>

          <div className="flex gap-2 pt-2">
            {project.demoUrl && (
              <Button size="sm" variant="default" className="flex-1">
                <Globe className="w-4 h-4 mr-1" />
                Demo
              </Button>
            )}
            {project.sourceUrl && (
              <Button size="sm" variant="outline" className="flex-1">
                <Code className="w-4 h-4 mr-1" />
                Code
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// ---------------
// Enhanced Search and Filter Component
// ---------------
const ProjectFilters = ({ query, setQuery, filter, setFilter, category, setCategory }) => {
  const techTags = ["All", ...new Set(profile.projects.flatMap((p) => p.stack))];
  const categories = ["All", ...new Set(profile.projects.map((p) => p.category))];

  return (
    <div className="space-y-4">
      <div className="flex flex-col lg:flex-row gap-4">
        <div className="flex items-center gap-2 flex-1">
          <Search className="w-5 h-5 text-muted-foreground" />
          <Input
            placeholder="Search projects by name, technology, or keywords..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="text-base"
          />
        </div>

        <div className="flex gap-3">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="border rounded-lg px-3 py-2 bg-background text-foreground"
            >
              {categories.map((cat) => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>

          <div className="flex items-center gap-2">
            <Code className="w-4 h-4 text-muted-foreground" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border rounded-lg px-3 py-2 bg-background text-foreground"
            >
              {techTags.map((tech) => (
                <option key={tech} value={tech}>{tech}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

// ---------------
// Main Portfolio Component
// ---------------
export default function Portfolio() {
  const { theme, setTheme } = useTheme();
  const [selectedIntent, setSelectedIntent] = useState(null);
  const [query, setQuery] = useState("");
  const [filter, setFilter] = useState("All");
  const [category, setCategory] = useState("All");

  // Enhanced filtering logic
  const filteredProjects = useMemo(() => {
    const q = query.toLowerCase();
    return profile.projects.filter((project) => {
      // Basic filters
      const matchesQuery =
        project.name.toLowerCase().includes(q) ||
        project.stack.join(" ").toLowerCase().includes(q) ||
        project.highlights.join(" ").toLowerCase().includes(q) ||
        project.tags.join(" ").toLowerCase().includes(q);

      const matchesTech = filter === "All" || project.stack.includes(filter);
      const matchesCategory = category === "All" || project.category === category;

      // Intent-based filtering
      const matchesIntent = !selectedIntent ||
        !project.relevantFor ||
        project.relevantFor.includes(selectedIntent);

      return matchesQuery && matchesTech && matchesCategory && matchesIntent;
    });
  }, [query, filter, category, selectedIntent]);

  const handleIntentSelection = (intent) => {
    setSelectedIntent(intent);
  };

  const resetIntentSelection = () => {
    setSelectedIntent(null);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Enhanced Navigation */}
      <div className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-background/80 border-b">
        <div className="max-w-6xl mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center gap-2 font-bold text-lg">
            <GitBranch className="w-6 h-6 text-primary" />
            <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Pavan.dev
            </span>
          </div>

          <nav className="hidden md:flex items-center gap-6">
            <a href="#about" className="text-sm font-medium hover:text-primary transition-colors">About</a>
            <a href="#projects" className="text-sm font-medium hover:text-primary transition-colors">Projects</a>
            <a href="#contact" className="text-sm font-medium hover:text-primary transition-colors">Contact</a>
          </nav>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="hover:bg-accent"
            >
              {theme === "dark" ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </Button>
            <a href={profile.links.github} target="_blank" rel="noreferrer">
              <Button variant="ghost" size="icon" className="hover:bg-accent">
                <Github className="w-5 h-5" />
              </Button>
            </a>
            <a href={profile.links.linkedin} target="_blank" rel="noreferrer">
              <Button variant="ghost" size="icon" className="hover:bg-accent">
                <Linkedin className="w-5 h-5" />
              </Button>
            </a>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <Section id="about" className="pt-20 pb-10">
        <AnimatePresence mode="wait">
          {!selectedIntent ? (
            <UserIntentSelector key="selector" onSelectIntent={handleIntentSelection} />
          ) : (
            <HeroSection key="hero" selectedIntent={selectedIntent} onReset={resetIntentSelection} />
          )}
        </AnimatePresence>
      </Section>

      {/* Projects Section */}
      <Section id="projects" title="Featured Projects" icon={Briefcase} className="bg-muted/30">
        <div className="space-y-8">
          <ProjectFilters
            query={query}
            setQuery={setQuery}
            filter={filter}
            setFilter={setFilter}
            category={category}
            setCategory={setCategory}
          />

          <div className="text-sm text-muted-foreground">
            Showing {filteredProjects.length} of {profile.projects.length} projects
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatePresence>
              {filteredProjects.map((project, idx) => (
                <ProjectCard
                  key={`${project.name}-${idx}`}
                  project={project}
                  selectedIntent={selectedIntent}
                />
              ))}
            </AnimatePresence>
          </div>

          {filteredProjects.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <div className="text-muted-foreground">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg">No projects found matching your criteria</p>
                <p className="text-sm">Try adjusting your search or filters</p>
              </div>
            </motion.div>
          )}
        </div>
      </Section>

      {/* Contact Section */}
      <Section id="contact" title="Get In Touch" icon={Mail} className="bg-background">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="grid md:grid-cols-2 gap-8"
          >
            <Card className="p-6">
              <CardHeader className="px-0 pt-0">
                <CardTitle className="flex items-center gap-2">
                  <Heart className="w-5 h-5 text-red-500" />
                  Let's Connect
                </CardTitle>
                <CardDescription>
                  Ready to discuss opportunities, projects, or just chat about technology
                </CardDescription>
              </CardHeader>
              <CardContent className="px-0 space-y-4">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                  <Mail className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">{profile.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                  <Phone className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">Phone</p>
                    <p className="text-sm text-muted-foreground">{profile.phone}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                  <MapPin className="w-5 h-5 text-primary" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">{profile.location}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardHeader className="px-0 pt-0">
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-500" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Choose the best way to connect based on your needs
                </CardDescription>
              </CardHeader>
              <CardContent className="px-0 space-y-3">
                <Button className="w-full justify-start" size="lg">
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
                <Button variant="outline" className="w-full justify-start" size="lg">
                  <Download className="w-4 h-4 mr-2" />
                  Download Resume
                </Button>
                <Button variant="outline" className="w-full justify-start" size="lg">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule Meeting
                </Button>
                <div className="flex gap-2 pt-2">
                  <Button variant="ghost" size="icon" className="flex-1" asChild>
                    <a href={profile.links.github} target="_blank" rel="noreferrer">
                      <Github className="w-5 h-5" />
                    </a>
                  </Button>
                  <Button variant="ghost" size="icon" className="flex-1" asChild>
                    <a href={profile.links.linkedin} target="_blank" rel="noreferrer">
                      <Linkedin className="w-5 h-5" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </Section>

      {/* Footer */}
      <footer className="border-t bg-muted/30">
        <div className="max-w-6xl mx-auto px-4 py-12">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="space-y-4">
              <div className="flex items-center gap-2 font-bold text-lg">
                <GitBranch className="w-5 h-5 text-primary" />
                <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                  Pavan.dev
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                Building innovative solutions with modern technology
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Quick Links</h3>
              <div className="space-y-2 text-sm">
                <a href="#about" className="block hover:text-primary transition-colors">About</a>
                <a href="#projects" className="block hover:text-primary transition-colors">Projects</a>
                <a href="#contact" className="block hover:text-primary transition-colors">Contact</a>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold">Connect</h3>
              <div className="flex gap-2">
                <Button variant="ghost" size="icon" asChild>
                  <a href={profile.links.github} target="_blank" rel="noreferrer">
                    <Github className="w-4 h-4" />
                  </a>
                </Button>
                <Button variant="ghost" size="icon" asChild>
                  <a href={profile.links.linkedin} target="_blank" rel="noreferrer">
                    <Linkedin className="w-4 h-4" />
                  </a>
                </Button>
                <Button variant="ghost" size="icon" asChild>
                  <a href={`mailto:${profile.email}`}>
                    <Mail className="w-4 h-4" />
                  </a>
                </Button>
              </div>
            </div>
          </div>

          <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>© {new Date().getFullYear()} {profile.name}. All rights reserved.</p>
            <p className="mt-1">Last updated: {profile.lastUpdated}</p>
          </div>
        </div>
      </footer>

      {/* Global Styles */}
      <style jsx global>{`
        :root {
          color-scheme: light dark;
        }
        html {
          scroll-behavior: smooth;
        }
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `}</style>
    </div>
  );
}
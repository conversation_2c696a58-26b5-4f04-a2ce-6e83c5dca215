# Professional Portfolio Website

A sophisticated, user-centric portfolio website that adapts its presentation based on visitor intent while maintaining a professional appearance throughout.

## Features

### 🎯 User Intent Capture
- Interactive homepage asking "Who are you looking to connect with?"
- Four distinct user types: Potential Employer, Client, Collaborator, General Visitor
- Dynamic content loading based on user selection

### 🔍 Advanced Search & Filtering
- Search through projects by technology, category, or keywords
- Filter by project type, technology stack, and categories
- Real-time filtering with instant results

### 💼 Professional Project Display
- Clean grid/card-based design with hover animations
- Project thumbnails and previews
- Technology stack badges and project tags
- Links to live demos and source code
- Award and featured project highlighting

### 🎨 Modern Design
- Responsive design for all devices
- Dark/light theme support
- Smooth animations and transitions
- Professional typography and color scheme
- Optimized for performance

### 📱 Dynamic Content
- Role-specific bio sections
- Curated project portfolios based on user intent
- Tailored call-to-action buttons
- Personalized user experience

## Tech Stack

- **Frontend**: React, Next.js
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **UI Components**: Custom component library

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <your-repo-url>
cd portfolio
```

2. Install dependencies
```bash
npm install
# or
yarn install
```

3. Run the development server
```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Building for Production

```bash
npm run build
npm start
```

## Customization

### Adding Projects
Edit the `profile.projects` array in `enhanced-portfolio.jsx` to add new projects with the following structure:

```javascript
{
  name: "Project Name",
  year: 2025,
  category: "Category",
  type: "featured|award|standard",
  stack: ["Tech1", "Tech2"],
  highlights: ["Description"],
  demoUrl: "https://demo.com",
  sourceUrl: "https://github.com/username/repo",
  image: "/path/to/image",
  tags: ["tag1", "tag2"],
  relevantFor: ["employer", "client", "collaborator"]
}
```

### Updating Personal Information
Modify the `profile` object in `enhanced-portfolio.jsx` to update:
- Personal details (name, title, contact info)
- Role-specific content for different user intents
- Social media links

### Styling
- Colors and themes: Edit `tailwind.config.js` and `globals.css`
- Component styles: Modify individual component files
- Animations: Adjust Framer Motion configurations

## Project Structure

```
portfolio/
├── components/ui/          # Reusable UI components
├── lib/                    # Utility functions
├── pages/                  # Next.js pages
├── enhanced-portfolio.jsx  # Main portfolio component
├── globals.css            # Global styles and CSS variables
├── tailwind.config.js     # Tailwind configuration
└── package.json           # Dependencies and scripts
```

## License

MIT License - feel free to use this template for your own portfolio!
